#!/usr/bin/env python3

import sys
sys.stdout = open(sys.stdout.fileno(), mode='w', buffering=1)
sys.stderr = open(sys.stderr.fileno(), mode='w', buffering=1)

import hydra
import argparse
from omegaconf import OmegaConf
import pathlib
from diffusion_policy_3d.workspace.base_workspace import BaseWorkspace
import tqdm
import torch
import os 
import numpy as np
from termcolor import cprint
from torch.utils.data import DataLoader
from diffusion_policy_3d.common.pytorch_util import dict_apply
import copy

os.environ['WANDB_SILENT'] = "True"
OmegaConf.register_new_resolver("eval", eval, replace=True)

# Joint names for the 25-dimensional action space
JOINT_NAMES = [
    # Waist (1 joint)
    "waist_pitch",              # 0: joint32[1] -> joint25[0]
    
    # Head (2 joints)  
    "head_pitch",               # 1: joint32[3] -> joint25[1]
    "head_yaw",                 # 2: joint32[5] -> joint25[2]
    
    # Left arm (5 joints out of 7)
    "left_arm_joint_0",         # 3: joint32[6] -> joint25[3]
    "left_arm_joint_1",         # 4: joint32[7] -> joint25[4]
    "left_arm_joint_2",         # 5: joint32[8] -> joint25[5]
    "left_arm_joint_3",         # 6: joint32[9] -> joint25[6]
    "left_arm_joint_4",         # 7: joint32[10] -> joint25[7]
    
    # Right arm (5 joints out of 7)
    "right_arm_joint_0",        # 8: joint32[13] -> joint25[8]
    "right_arm_joint_1",        # 9: joint32[14] -> joint25[9]
    "right_arm_joint_2",        # 10: joint32[15] -> joint25[10]
    "right_arm_joint_3",        # 11: joint32[16] -> joint25[11]
    "right_arm_joint_4",        # 12: joint32[17] -> joint25[12]
    
    # Left hand (6 joints)
    "left_hand_joint_0",        # 13: joint32[20] -> joint25[13]
    "left_hand_joint_1",        # 14: joint32[21] -> joint25[14]
    "left_hand_joint_2",        # 15: joint32[22] -> joint25[15]
    "left_hand_joint_3",        # 16: joint32[23] -> joint25[16]
    "left_hand_joint_4",        # 17: joint32[24] -> joint25[17]
    "left_hand_joint_5",        # 18: joint32[25] -> joint25[18]
    
    # Right hand (6 joints)
    "right_hand_joint_0",       # 19: joint32[26] -> joint25[19]
    "right_hand_joint_1",       # 20: joint32[27] -> joint25[20]
    "right_hand_joint_2",       # 21: joint32[28] -> joint25[21]
    "right_hand_joint_3",       # 22: joint32[29] -> joint25[22]
    "right_hand_joint_4",       # 23: joint32[30] -> joint25[23]
    "right_hand_joint_5",       # 24: joint32[31] -> joint25[24]
]

def create_custom_dataset(dataset, episode_ids):
    """Create a dataset with only specified episodes"""
    from diffusion_policy_3d.common.sampler import SequenceSampler
    
    # Create episode mask for specified episodes
    n_episodes = dataset.replay_buffer.n_episodes
    episode_mask = np.zeros(n_episodes, dtype=bool)
    
    # Validate episode IDs
    for ep_id in episode_ids:
        if ep_id >= n_episodes or ep_id < 0:
            raise ValueError(f"Invalid episode ID {ep_id}. Dataset has {n_episodes} episodes (0-{n_episodes-1})")
        episode_mask[ep_id] = True
    
    # Create new dataset with custom sampler
    custom_dataset = copy.copy(dataset)
    custom_dataset.sampler = SequenceSampler(
        replay_buffer=dataset.replay_buffer,
        sequence_length=dataset.horizon,
        pad_before=dataset.pad_before,
        pad_after=dataset.pad_after,
        episode_mask=episode_mask
    )
    
    return custom_dataset

def evaluate_specific_episodes(workspace, cfg, episode_ids):
    """Evaluate model on specific episodes"""
    cprint("=" * 60, "green")
    cprint("Specific Episodes Joint Angle Evaluation", "green")
    cprint("=" * 60, "green")
    
    # Load model
    policy = workspace.get_model()
    policy.eval()
    
    device = torch.device(cfg.training.device)
    policy = policy.to(device)
    
    # Setup dataset
    dataset = hydra.utils.instantiate(cfg.task.dataset)
    
    # Print episode info
    cprint(f"Dataset info:", "yellow")
    cprint(f"  Total episodes: {dataset.replay_buffer.n_episodes}", "cyan")
    episode_lengths = [len(dataset.replay_buffer.get_episode(i)['action']) for i in range(dataset.replay_buffer.n_episodes)]
    cprint(f"  Episode lengths: {episode_lengths}", "cyan")
    cprint(f"  Evaluating episodes: {episode_ids}", "green")
    
    # Create custom dataset with specified episodes
    eval_dataset = create_custom_dataset(dataset, episode_ids)
    eval_dataloader = DataLoader(eval_dataset, **cfg.val_dataloader)
    
    cprint(f"Evaluation dataset size: {len(eval_dataset)}", "cyan")
    cprint(f"Batch size: {cfg.val_dataloader.batch_size}", "cyan")
    
    # Get normalizer for unnormalizing actions
    normalizer = dataset.get_normalizer()
    
    # Collect all predictions and ground truth (unnormalized)
    all_pred_actions = []
    all_gt_actions = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm.tqdm(eval_dataloader, desc="Evaluating")):
            # Move data to device
            def to_device_recursive(obj):
                if isinstance(obj, torch.Tensor):
                    return obj.to(device, non_blocking=True)
                elif isinstance(obj, dict):
                    return {k: to_device_recursive(v) for k, v in obj.items()}
                elif isinstance(obj, (list, tuple)):
                    return type(obj)(to_device_recursive(item) for item in obj)
                else:
                    return obj
            
            batch = to_device_recursive(batch)
            obs_dict = batch['obs']
            gt_action = batch['action']
            
            try:
                # Model prediction
                result = policy.predict_action(obs_dict)
                pred_action = result['action_pred']
                
                # Unnormalize both predictions and ground truth to get real joint angles
                pred_action_real = normalizer['action'].unnormalize(pred_action)
                gt_action_real = normalizer['action'].unnormalize(gt_action)
                
                # Convert to numpy and store
                all_pred_actions.append(pred_action_real.cpu().numpy())
                all_gt_actions.append(gt_action_real.cpu().numpy())
                
            except Exception as e:
                cprint(f"Batch {batch_idx} failed: {e}", "red")
                continue
            
            # Limit evaluation batches if specified
            if cfg.training.max_val_steps is not None and batch_idx >= cfg.training.max_val_steps - 1:
                break
    
    if not all_pred_actions:
        cprint("Evaluation failed: No successful batches", "red")
        return None
    
    # Concatenate all data
    all_pred_actions = np.concatenate(all_pred_actions, axis=0)  # [N, T, 25]
    all_gt_actions = np.concatenate(all_gt_actions, axis=0)      # [N, T, 25]
    
    N, T, action_dim = all_pred_actions.shape
    cprint(f"Total samples: {N}, Time steps: {T}, Action dimensions: {action_dim}", "cyan")
    
    # Calculate joint angle errors
    joint_errors = analyze_joint_angle_errors(all_pred_actions, all_gt_actions)
    
    # Save results
    output_dir = pathlib.Path(workspace.output_dir)
    results_file = output_dir / f'joint_angle_evaluation_episodes_{"_".join(map(str, episode_ids))}.npz'
    
    results = {
        'pred_actions': all_pred_actions,
        'gt_actions': all_gt_actions,
        'joint_names': JOINT_NAMES,
        'episode_ids': episode_ids,
        **joint_errors
    }
    
    np.savez(results_file, **results)
    cprint(f"Results saved to: {results_file}", "green")
    
    return results

def analyze_joint_angle_errors(pred_actions, gt_actions):
    """Analyze joint angle errors in detail"""
    N, T, action_dim = pred_actions.shape
    
    # Calculate absolute errors for each joint at each timestep
    abs_errors = np.abs(pred_actions - gt_actions)  # [N, T, 25]
    
    # Overall statistics
    overall_mae = np.mean(abs_errors)
    overall_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2))
    
    # Per-joint statistics (averaged over all samples and timesteps)
    joint_mae = np.mean(abs_errors, axis=(0, 1))  # [25,]
    joint_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2, axis=(0, 1)))  # [25,]
    joint_max_error = np.max(abs_errors, axis=(0, 1))  # [25,]
    
    # Per-timestep statistics (averaged over all samples and joints)
    timestep_mae = np.mean(abs_errors, axis=(0, 2))  # [T,]
    timestep_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2, axis=(0, 2)))  # [T,]
    
    # Print detailed analysis
    cprint("=" * 60, "cyan")
    cprint("Joint Angle Error Analysis", "cyan")
    cprint("=" * 60, "cyan")
    
    cprint(f"Overall MAE: {overall_mae:.4f} radians ({np.degrees(overall_mae):.2f} degrees)", "green")
    cprint(f"Overall RMSE: {overall_rmse:.4f} radians ({np.degrees(overall_rmse):.2f} degrees)", "green")
    
    cprint(f"\nPer-Joint Analysis (all {action_dim} joints):", "yellow")
    cprint("-" * 80, "white")
    cprint(f"{'Joint':<25} {'MAE (rad)':<12} {'MAE (deg)':<12} {'RMSE (rad)':<12} {'RMSE (deg)':<12} {'Max Error (deg)':<15}", "white")
    cprint("-" * 80, "white")
    
    for i in range(action_dim):
        joint_name = JOINT_NAMES[i] if i < len(JOINT_NAMES) else f"joint_{i}"
        cprint(f"{joint_name:<25} {joint_mae[i]:<12.4f} {np.degrees(joint_mae[i]):<12.2f} "
               f"{joint_rmse[i]:<12.4f} {np.degrees(joint_rmse[i]):<12.2f} {np.degrees(joint_max_error[i]):<15.2f}", "white")
    
    # Find best and worst joints
    best_joint_idx = np.argmin(joint_mae)
    worst_joint_idx = np.argmax(joint_mae)
    
    cprint(f"\nBest Joint: {JOINT_NAMES[best_joint_idx]} (MAE: {np.degrees(joint_mae[best_joint_idx]):.2f} deg)", "green")
    cprint(f"Worst Joint: {JOINT_NAMES[worst_joint_idx]} (MAE: {np.degrees(joint_mae[worst_joint_idx]):.2f} deg)", "red")
    
    return {
        'overall_mae': overall_mae,
        'overall_rmse': overall_rmse,
        'joint_mae': joint_mae,
        'joint_rmse': joint_rmse,
        'joint_max_error': joint_max_error,
        'timestep_mae': timestep_mae,
        'timestep_rmse': timestep_rmse,
        'abs_errors': abs_errors
    }

def load_config_with_defaults(config_name: str):
    """Load config with proper defaults resolution"""
    config_dir = pathlib.Path(__file__).parent.joinpath('diffusion_policy_3d', 'config')
    config_path = config_dir / f'{config_name}.yaml'

    # Load main config
    cfg = OmegaConf.load(config_path)
    print(f"Loaded main config from: {config_path}")
    print(f"Config keys: {list(cfg.keys())}")

    # Check if config has defaults
    if 'defaults' in cfg:
        print(f"Found defaults: {cfg.defaults}")
        # Process defaults
        for default in cfg.defaults:
            print(f"Processing default: {default}, type: {type(default)}")
            if isinstance(default, dict):
                print(f"Default is dict with items: {list(default.items())}")
                for key, value in default.items():
                    print(f"Processing key: {key}, value: {value}")
                    if key == 'task':
                        # Load task config
                        task_config_path = config_dir / 'task' / f'{value}.yaml'
                        print(f"Loading task config from: {task_config_path}")
                        print(f"Task config path exists: {task_config_path.exists()}")
                        if task_config_path.exists():
                            task_cfg = OmegaConf.load(task_config_path)
                            cfg.task = task_cfg
                            print(f"Task config loaded successfully")
                        else:
                            print(f"Task config file not found: {task_config_path}")
            elif isinstance(default, str) and default != '_self_':
                # Handle other defaults if needed
                print(f"Default is string: {default}")
            else:
                print(f"Default is other type: {type(default)}")
    else:
        print("No defaults found in config")

    print(f"Final config keys: {list(cfg.keys())}")
    return cfg

def main():
    parser = argparse.ArgumentParser(description='Evaluate model on specific episodes')
    parser.add_argument('--config-name', type=str, required=True, help='Config name')
    parser.add_argument('--model-dir', type=str, required=True, help='Model directory')
    parser.add_argument('--data-path', type=str, required=True, help='Dataset path')
    parser.add_argument('--episodes', type=str, required=True, help='Episode IDs (comma-separated, e.g., "0,1,2")')
    parser.add_argument('--device', type=str, default='cuda:0', help='Device')

    args = parser.parse_args()

    # Parse episode IDs
    episode_ids = [int(x.strip()) for x in args.episodes.split(',')]

    # Load config with proper defaults resolution
    cfg = load_config_with_defaults(args.config_name)

    # Override config
    cfg.hydra = OmegaConf.create({'run': {'dir': args.model_dir}})
    cfg.task.dataset.zarr_path = args.data_path
    cfg.training.device = args.device

    OmegaConf.resolve(cfg)
    
    cprint(f"Config: {cfg._target_}", "yellow")
    cprint(f"Model dir: {args.model_dir}", "yellow")
    cprint(f"Data path: {args.data_path}", "yellow")
    cprint(f"Episodes: {episode_ids}", "yellow")
    
    cls = hydra.utils.get_class(cfg._target_)
    workspace: BaseWorkspace = cls(cfg)
    
    # Check if model exists
    checkpoint_path = workspace.get_checkpoint_path(tag='latest')
    if not checkpoint_path.exists():
        cprint(f"Error: Model file not found {checkpoint_path}", "red")
        return
    
    cprint(f"Using model: {checkpoint_path}", "green")
    
    # Start evaluation
    results = evaluate_specific_episodes(workspace, cfg, episode_ids)
    
    if results:
        cprint("Specific episodes evaluation completed!", "green")
    else:
        cprint("Specific episodes evaluation failed!", "red")

if __name__ == "__main__":
    main()
